#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create gender-stratified ROC curves for different models.
"""

import pandas as pd
import argparse
from osa_analysis.data.database import DatabaseConnector
from osa_analysis.data.queries import DataExtractor
from osa_analysis.analysis.processor import DataProcessor
from osa_analysis.analysis.statistics import StatisticsGenerator
from osa_analysis.analysis.stop_bang import StopBangAnalyzer

def load_and_merge_data():
    """Load and merge data following the same pattern as main.py"""
    # Connect to the database
    db_connector = DatabaseConnector('../patient_management.db')
    conn = db_connector.get_connection()

    # Extract data
    print("Extracting data from database...")
    extractor = DataExtractor(conn)

    # Get patient data
    osa_patients = extractor.get_osa_patients()
    suspected_osa_patients = extractor.get_suspected_osa_patients()
    unsuspected_osa_patients = extractor.get_unsuspected_osa_patients()
    sleep_study_patients = extractor.get_sleep_study_patients()
    demographics = extractor.get_patient_demographics()
    hypertension_patients = extractor.get_hypertension_patients()
    antihypertensive_patients = extractor.get_patients_on_antihypertensives()
    diabetes_patients = extractor.get_diabetes_patients()
    cardiovascular_patients = extractor.get_cardiovascular_patients()
    bmi_data = extractor.get_bmi_data()

    # Get medication data
    med_data = extractor.get_medication_data()
    med_classes, atc_classes = extractor.get_medication_by_atc_class()

    # Close database connection
    db_connector.close_connection()

    # Process data
    print("Processing data...")
    processor = DataProcessor()
    df = processor.merge_data(
        sleep_study_patients,
        osa_patients,
        suspected_osa_patients,
        unsuspected_osa_patients,
        demographics,
        hypertension_patients,
        antihypertensive_patients,
        med_classes,
        diabetes_patients,
        cardiovascular_patients,
        bmi_data
    )

    return df, atc_classes

def main():
    """Create gender-stratified ROC curves for specified model."""

    parser = argparse.ArgumentParser(description='Create gender-stratified ROC curves')
    parser.add_argument('--model', choices=['stop_bang', 'random_forest'],
                       default='random_forest',
                       help='Model to analyze (default: random_forest)')

    args = parser.parse_args()

    # Load the data
    print("Loading data...")
    df, atc_classes = load_and_merge_data()

    # Initialize statistics generator
    stats_generator = StatisticsGenerator()

    if args.model == 'stop_bang':
        print("Calculating STOP-BANG scores...")
        stop_bang_analyzer = StopBangAnalyzer()
        stop_bang_df = stop_bang_analyzer.analyze(df)

        print("Creating gender-stratified ROC curves for STOP-BANG model...")
        gender_metrics = stats_generator.create_gender_stratified_roc_curve(
            df=stop_bang_df,
            model_column='partial_stop_bang_score',
            save_path='plots/stop_bang_gender_roc.png'
        )

    elif args.model == 'random_forest':
        print("Creating gender-stratified ROC curves for Random Forest model...")
        gender_metrics = stats_generator.create_gender_stratified_roc_curve_rf(
            df=df,
            atc_classes=atc_classes,
            save_path='plots/rf_gender_roc.png'
        )

    print(f"\nAnalysis complete for {args.model} model!")
    print("Check the 'plots' directory for the generated ROC curve.")

if __name__ == "__main__":
    main()
