#!/usr/bin/env python3
"""
<PERSON><PERSON>t to create gender-stratified ROC curves for different models with enhanced visualizations.
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import os
from sklearn.metrics import roc_curve, auc
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from osa_analysis.data.database import DatabaseConnector
from osa_analysis.data.queries import DataExtractor
from osa_analysis.analysis.processor import DataProcessor
from osa_analysis.analysis.statistics import StatisticsGenerator
from osa_analysis.analysis.stop_bang import StopBangAnalyzer

def load_and_merge_data():
    """Load and merge data following the same pattern as main.py"""
    # Connect to the database
    db_connector = DatabaseConnector('../patient_management.db')
    conn = db_connector.get_connection()

    # Extract data
    print("Extracting data from database...")
    extractor = DataExtractor(conn)

    # Get patient data
    osa_patients = extractor.get_osa_patients()
    suspected_osa_patients = extractor.get_suspected_osa_patients()
    unsuspected_osa_patients = extractor.get_unsuspected_osa_patients()
    sleep_study_patients = extractor.get_sleep_study_patients()
    demographics = extractor.get_patient_demographics()
    hypertension_patients = extractor.get_hypertension_patients()
    antihypertensive_patients = extractor.get_patients_on_antihypertensives()
    diabetes_patients = extractor.get_diabetes_patients()
    cardiovascular_patients = extractor.get_cardiovascular_patients()
    bmi_data = extractor.get_bmi_data()

    # Get medication data
    med_data = extractor.get_medication_data()
    med_classes, atc_classes = extractor.get_medication_by_atc_class()

    # Close database connection
    db_connector.close_connection()

    # Process data
    print("Processing data...")
    processor = DataProcessor()
    df = processor.merge_data(
        sleep_study_patients,
        osa_patients,
        suspected_osa_patients,
        unsuspected_osa_patients,
        demographics,
        hypertension_patients,
        antihypertensive_patients,
        med_classes,
        diabetes_patients,
        cardiovascular_patients,
        bmi_data
    )

    return df, atc_classes

class EnhancedVisualizationGenerator:
    """Enhanced visualization generator with configurable font sizes and additional plots."""

    def __init__(self, font_size=16):
        """Initialize with configurable font size."""
        self.font_size = font_size
        # Set global font sizes
        plt.rcParams.update({
            'font.size': font_size,
            'axes.titlesize': font_size + 2,
            'axes.labelsize': font_size,
            'xtick.labelsize': font_size - 2,
            'ytick.labelsize': font_size - 2,
            'legend.fontsize': font_size - 2,
            'figure.titlesize': font_size + 4
        })

    def create_enhanced_roc_curve(self, df, model_column, model_name, save_path, atc_classes=None):
        """Create enhanced square ROC curves with better formatting."""
        print(f"\n{'-'*50}")
        print(f"ENHANCED GENDER-STRATIFIED ROC ANALYSIS FOR {model_name.upper()}")
        print(f"{'-'*50}")

        # Check if required columns exist (skip for Random Forest as it's handled differently)
        if model_name != 'Random Forest' and model_column not in df.columns:
            print(f"Error: Model column '{model_column}' not found in dataset")
            return

        if 'Sex' not in df.columns:
            print("Error: Gender data ('Sex' column) not found in dataset")
            return

        if 'has_osa' not in df.columns:
            print("Error: OSA outcome ('has_osa' column) not found in dataset")
            return

        # Filter to patients with complete data
        if model_name == 'Random Forest':
            # For Random Forest, we'll filter based on the required model columns later
            complete_data = df.dropna(subset=['Sex', 'has_osa'])
        else:
            complete_data = df.dropna(subset=[model_column, 'Sex', 'has_osa'])

        if len(complete_data) == 0:
            print("Error: No patients with complete data for analysis")
            return

        # Create plots directory if it doesn't exist
        os.makedirs('plots', exist_ok=True)

        # Create square figure for better interpretation
        fig, ax = plt.subplots(figsize=(10, 10))

        # For Random Forest, train a single model and get predictions for all patients
        if model_name == 'Random Forest':
            rf_predictions, splits = self._train_single_rf_model(complete_data, atc_classes)
            complete_data = complete_data.copy()
            complete_data['rf_predictions'] = rf_predictions
            complete_data['split'] = splits

        # Calculate ROC curves for each gender
        gender_metrics = {}
        colors = {'männlich': '#1f77b4', 'weiblich': '#ff7f0e'}  # Better colors

        for gender in ['männlich', 'weiblich']:
            gender_data = complete_data[complete_data['Sex'] == gender]

            if len(gender_data) > 1 and gender_data['has_osa'].nunique() > 1:
                if model_name == 'Random Forest':
                    # Use predictions from single trained model
                    fpr, tpr, thresholds = roc_curve(gender_data['has_osa'], gender_data['rf_predictions'])
                    roc_auc = auc(fpr, tpr)
                else:
                    # Calculate ROC curve for other models
                    fpr, tpr, thresholds = roc_curve(gender_data['has_osa'], gender_data[model_column])
                    roc_auc = auc(fpr, tpr)

                # Store metrics
                gender_metrics[gender] = {
                    'auc': roc_auc,
                    'n_patients': len(gender_data),
                    'n_osa': gender_data['has_osa'].sum(),
                    'osa_prevalence': gender_data['has_osa'].mean()
                }

                # Plot ROC curve with thicker lines
                ax.plot(fpr, tpr, color=colors[gender], lw=3,
                       label=f'{gender} (AUC = {roc_auc:.3f}, n={len(gender_data)})')

                print(f"\n{gender} patients:")
                print(f"  Sample size: {len(gender_data)}")
                print(f"  OSA cases: {gender_data['has_osa'].sum()} ({gender_data['has_osa'].mean()*100:.1f}%)")
                print(f"  AUC: {roc_auc:.3f}")
            else:
                print(f"\n{gender} patients: Insufficient data for ROC analysis")

        # Calculate overall ROC curve
        if model_name == 'Random Forest':
            fpr_overall, tpr_overall, _ = roc_curve(complete_data['has_osa'], complete_data['rf_predictions'])
            roc_auc_overall = auc(fpr_overall, tpr_overall)
        else:
            fpr_overall, tpr_overall, _ = roc_curve(complete_data['has_osa'], complete_data[model_column])
            roc_auc_overall = auc(fpr_overall, tpr_overall)

        ax.plot(fpr_overall, tpr_overall, color='#2ca02c', lw=3, linestyle='--',
               label=f'Overall (AUC = {roc_auc_overall:.3f}, n={len(complete_data)})')

        print(f"\nOverall performance:")
        print(f"  Sample size: {len(complete_data)}")
        print(f"  OSA cases: {complete_data['has_osa'].sum()} ({complete_data['has_osa'].mean()*100:.1f}%)")
        print(f"  AUC: {roc_auc_overall:.3f}")

        # Add diagonal reference line
        ax.plot([0, 1], [0, 1], color='#d62728', lw=2, linestyle=':')

        # Format plot with square aspect ratio
        ax.set_xlim(0.0, 1.0)
        ax.set_ylim(0.0, 1.0)
        ax.set_xlabel('False Positive Rate (1 - Specificity)', fontsize=self.font_size)
        ax.set_ylabel('True Positive Rate (Sensitivity)', fontsize=self.font_size)
        ax.set_title(f'Gender-Stratified ROC Curves: {model_name}', fontsize=self.font_size + 2)
        ax.legend(loc="lower right", fontsize=self.font_size - 2)
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal', adjustable='box')  # Make it truly square

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"\nROC curve plot saved to: {save_path}")
        plt.close()

        return gender_metrics

    def _train_single_rf_model(self, df, atc_classes):
        """Train a single Random Forest model and return predictions for all patients."""
        # Prepare model columns
        model_cols = ['Age', 'has_hypertension', 'has_diabetes',
                     'has_cardiovascular', 'on_antihypertensives']

        # Add specific antihypertensive medication classes
        for name in atc_classes.values():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in df.columns:
                model_cols.append(col_name)

        # Add sex as dummy variable if available
        df_copy = df.copy()
        if 'Sex' in df_copy.columns and df_copy['Sex'].notna().any():
            df_copy['is_male'] = (df_copy['Sex'] == 'männlich').astype(int)
            model_cols.append('is_male')

        # Add BMI if available
        if 'BMI' in df_copy.columns and df_copy['BMI'].notna().any():
            model_cols.append('BMI')

        # Filter to patients with complete data for modeling
        model_df = df_copy[['has_osa'] + model_cols].dropna()

        if len(model_df) < 20:
            return np.zeros(len(df))

        # Train Random Forest model on complete dataset
        X = model_df[model_cols]
        y = model_df['has_osa']

        # Split for training and validation
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )

        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X_train, y_train)

        # Get predictions for all patients in the original dataframe
        # First, prepare features for all patients
        df_for_prediction = df_copy[model_cols].fillna(0)  # Fill missing with 0 for prediction
        predictions = rf.predict_proba(df_for_prediction)[:, 1]
        df_for_prediction["split"] = "training"
        df_for_prediction.loc[df_copy.index.isin(X_test.index), "split"] = "validation"

        return predictions, df_for_prediction["split"]

    def calculate_subgroup_metrics(self, df, model_column, model_name, atc_classes=None):
        """Calculate specificity and sensitivity for different subgroups."""
        print(f"\n{'-'*50}")
        print(f"SUBGROUP SENSITIVITY AND SPECIFICITY ANALYSIS FOR {model_name.upper()}")
        print(f"{'-'*50}")

        # Prepare data based on model type
        if model_name == 'Random Forest':
            # For Random Forest, train a single model and get predictions for all patients
            rf_predictions, splits = self._train_single_rf_model(df, atc_classes)
            analysis_df = df.copy()
            analysis_df['predictions'] = rf_predictions
            analysis_df['split'] = splits
            training_df = analysis_df.query("split == 'training'").reset_index(drop=True)
            analysis_df = analysis_df.query("split == 'validation'").reset_index(drop=True)
            # Filter to patients with complete demographic data for subgroup analysis
            analysis_df = analysis_df.dropna(subset=['has_osa', 'Sex', 'Age', 'BMI']).reset_index(drop=True)
        else:
            # For other models, use the model column directly
            analysis_df = df.dropna(subset=[model_column, 'has_osa']).copy()
            analysis_df = analysis_df.reset_index(drop=True)
            training_df = analysis_df
            analysis_df['predictions'] = analysis_df[model_column]

        if len(analysis_df) == 0:
            print("No data available for subgroup analysis")
            return

        # Define optimal threshold (using Youden's index)
        from sklearn.metrics import roc_curve
        fpr, tpr, thresholds = roc_curve(training_df['has_osa'], training_df['predictions'])
        optimal_idx = np.argmax(tpr - fpr)
        optimal_threshold = thresholds[optimal_idx]
        optimal_threshold = 0.5

        print(f"Using optimal threshold (based on training): {optimal_threshold:.3f}")

        # Create binary predictions
        analysis_df['binary_predictions'] = (analysis_df['predictions'] >= optimal_threshold).astype(int)

        # Calculate metrics for different subgroups
        self._calculate_metrics_for_subgroups(analysis_df)

        return analysis_df

    def _prepare_rf_data_for_analysis(self, df, atc_classes):
        """Prepare Random Forest data and get predictions for all patients."""
        # Prepare model columns
        model_cols = ['Age', 'has_hypertension', 'has_diabetes',
                     'has_cardiovascular', 'on_antihypertensives']

        # Add specific antihypertensive medication classes
        for name in atc_classes.values():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in df.columns:
                model_cols.append(col_name)

        # Add sex as dummy variable if available
        df_copy = df.copy()
        if 'Sex' in df_copy.columns and df_copy['Sex'].notna().any():
            df_copy['is_male'] = (df_copy['Sex'] == 'männlich').astype(int)
            model_cols.append('is_male')

        # Add BMI if available
        if 'BMI' in df_copy.columns and df_copy['BMI'].notna().any():
            model_cols.append('BMI')

        # Filter to patients with complete data
        model_df = df_copy[['has_osa', 'Sex', 'Age', 'BMI'] + model_cols].dropna()

        if len(model_df) < 20:
            return pd.DataFrame()

        # Reset index to avoid duplicate labels
        model_df = model_df.reset_index(drop=True)

        # Train Random Forest model on full dataset
        X = model_df[model_cols]
        y = model_df['has_osa']

        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)

        # Get predictions for all patients
        predictions = rf.predict_proba(X)[:, 1]
        model_df['predictions'] = predictions

        return model_df

    def _calculate_metrics_for_subgroups(self, df):
        """Calculate sensitivity and specificity for different subgroups."""
        from sklearn.metrics import confusion_matrix, classification_report

        def calculate_sens_spec(y_true, y_pred):
            tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
            sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
            specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
            return sensitivity, specificity, tp, tn, fp, fn

        # Overall metrics
        print(f"\n1. OVERALL METRICS:")
        sens, spec, tp, tn, fp, fn = calculate_sens_spec(df['has_osa'], df['binary_predictions'])
        print(f"   Sample size: {len(df)}")
        print(f"   OSA prevalence: {df['has_osa'].mean()*100:.1f}%")
        print(f"   Sensitivity: {sens:.3f}")
        print(f"   Specificity: {spec:.3f}")
        print(f"   TP: {tp}, TN: {tn}, FP: {fp}, FN: {fn}")

        # Gender-based metrics
        print(f"\n2. GENDER-BASED METRICS:")
        if 'Sex' in df.columns:
            for gender in ['männlich', 'weiblich']:
                gender_data = df[df['Sex'] == gender]
                if len(gender_data) > 10:
                    sens, spec, tp, tn, fp, fn = calculate_sens_spec(
                        gender_data['has_osa'], gender_data['binary_predictions'])
                    print(f"   {gender}:")
                    print(f"     Sample size: {len(gender_data)}")
                    print(f"     OSA prevalence: {gender_data['has_osa'].mean()*100:.1f}%")
                    print(f"     Sensitivity: {sens:.3f}")
                    print(f"     Specificity: {spec:.3f}")
                    print(f"     TP: {tp}, TN: {tn}, FP: {fp}, FN: {fn}")

        # BMI-based metrics
        print(f"\n3. BMI-BASED METRICS:")
        if 'BMI' in df.columns:
            try:
                bmi_low_indices = df.index[df['BMI'] < 30].tolist()
                bmi_high_indices = df.index[df['BMI'] >= 30].tolist()

                for bmi_group, indices in [('BMI < 30', bmi_low_indices), ('BMI ≥ 30', bmi_high_indices)]:
                    if len(indices) > 10:
                        bmi_data = df.iloc[indices]
                        sens, spec, tp, tn, fp, fn = calculate_sens_spec(
                            bmi_data['has_osa'], bmi_data['binary_predictions'])
                        print(f"   {bmi_group}:")
                        print(f"     Sample size: {len(bmi_data)}")
                        print(f"     OSA prevalence: {bmi_data['has_osa'].mean()*100:.1f}%")
                        print(f"     Sensitivity: {sens:.3f}")
                        print(f"     Specificity: {spec:.3f}")
                        print(f"     TP: {tp}, TN: {tn}, FP: {fp}, FN: {fn}")
            except Exception as e:
                print(f"   Error in BMI analysis: {e}")

        # Age-based metrics
        print(f"\n4. AGE-BASED METRICS:")
        if 'Age' in df.columns:
            try:
                age_low_indices = df.index[df['Age'] < 50].tolist()
                age_high_indices = df.index[df['Age'] >= 50].tolist()

                for age_group, indices in [('Age < 50', age_low_indices), ('Age ≥ 50', age_high_indices)]:
                    if len(indices) > 10:
                        age_data = df.iloc[indices]
                        sens, spec, tp, tn, fp, fn = calculate_sens_spec(
                            age_data['has_osa'], age_data['binary_predictions'])
                        print(f"   {age_group}:")
                        print(f"     Sample size: {len(age_data)}")
                        print(f"     OSA prevalence: {age_data['has_osa'].mean()*100:.1f}%")
                        print(f"     Sensitivity: {sens:.3f}")
                        print(f"     Specificity: {spec:.3f}")
                        print(f"     TP: {tp}, TN: {tn}, FP: {fp}, FN: {fn}")
            except Exception as e:
                print(f"   Error in Age analysis: {e}")

        # Combined subgroup analysis
        print(f"\n5. COMBINED SUBGROUP ANALYSIS:")
        if 'Sex' in df.columns and 'BMI' in df.columns and 'Age' in df.columns:
            try:
                subgroups = [
                    ('Male, BMI≥30, Age≥50', df.index[(df['Sex'] == 'männlich') & (df['BMI'] >= 30) & (df['Age'] >= 50)].tolist()),
                    ('Female, BMI≥30, Age≥50', df.index[(df['Sex'] == 'weiblich') & (df['BMI'] >= 30) & (df['Age'] >= 50)].tolist()),
                    ('Male, BMI<30, Age<50', df.index[(df['Sex'] == 'männlich') & (df['BMI'] < 30) & (df['Age'] < 50)].tolist()),
                    ('Female, BMI<30, Age<50', df.index[(df['Sex'] == 'weiblich') & (df['BMI'] < 30) & (df['Age'] < 50)].tolist()),
                ]

                for group_name, indices in subgroups:
                    if len(indices) > 5:
                        group_data = df.iloc[indices]
                        sens, spec, tp, tn, fp, fn = calculate_sens_spec(
                            group_data['has_osa'], group_data['binary_predictions'])
                        print(f"   {group_name}:")
                        print(f"     Sample size: {len(group_data)}")
                        print(f"     OSA prevalence: {group_data['has_osa'].mean()*100:.1f}%")
                        print(f"     Sensitivity: {sens:.3f}")
                        print(f"     Specificity: {spec:.3f}")
                        print(f"     TP: {tp}, TN: {tn}, FP: {fp}, FN: {fn}")
            except Exception as e:
                print(f"   Error in combined subgroup analysis: {e}")

    def _calculate_rf_roc(self, data, atc_classes):
        """Calculate ROC curve for Random Forest model."""
        # Prepare data for modeling (same as in StatisticsGenerator)
        model_cols = ['Age', 'has_hypertension', 'has_diabetes',
                     'has_cardiovascular', 'on_antihypertensives']

        # Add specific antihypertensive medication classes
        for name in atc_classes.values():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in data.columns:
                model_cols.append(col_name)

        # Add sex as dummy variable if available
        if 'Sex' in data.columns and data['Sex'].notna().any():
            data = data.copy()
            data['is_male'] = (data['Sex'] == 'männlich').astype(int)
            model_cols.append('is_male')

        # Add BMI if available
        if 'BMI' in data.columns and data['BMI'].notna().any():
            model_cols.append('BMI')

        # Filter to patients with complete data
        model_df = data[['has_osa'] + model_cols].dropna()

        if len(model_df) < 10:
            return [0], [0], 0.5

        # Prepare features and target
        X = model_df[model_cols]
        y = model_df['has_osa']

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )

        # Train Random Forest model
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X_train, y_train)

        # Get predictions
        y_prob = rf.predict_proba(X_test)[:, 1]

        # Calculate ROC curve
        fpr, tpr, thresholds = roc_curve(y_test, y_prob)
        roc_auc = auc(fpr, tpr)

        return fpr, tpr, roc_auc

    def create_distribution_plots(self, df):
        """Create simple distribution plots for age, BMI, and hypertension for complete population."""
        print(f"\n{'-'*50}")
        print("CREATING DISTRIBUTION PLOTS")
        print(f"{'-'*50}")

        os.makedirs('plots', exist_ok=True)

        # Age distribution
        if 'Age' in df.columns and df['Age'].notna().any():
            plt.figure(figsize=(10, 6))
            sns.histplot(data=df, x='Age', bins=30, kde=True, color='steelblue')
            plt.title('Age Distribution - Complete Population', fontsize=self.font_size + 2)
            plt.xlabel('Age (years)', fontsize=self.font_size)
            plt.ylabel('Count', fontsize=self.font_size)
            plt.tight_layout()
            plt.savefig('plots/age_distribution_simple.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("Age distribution plot saved to: plots/age_distribution_simple.png")

        # BMI distribution
        if 'BMI' in df.columns and df['BMI'].notna().any():
            plt.figure(figsize=(10, 6))
            sns.histplot(data=df, x='BMI', bins=30, kde=True, color='green')
            plt.title('BMI Distribution - Complete Population', fontsize=self.font_size + 2)
            plt.xlabel('BMI (kg/m²)', fontsize=self.font_size)
            plt.ylabel('Count', fontsize=self.font_size)
            plt.tight_layout()
            plt.savefig('plots/bmi_distribution_simple.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("BMI distribution plot saved to: plots/bmi_distribution_simple.png")

        # Hypertension distribution
        if 'has_hypertension' in df.columns:
            plt.figure(figsize=(8, 6))
            hypertension_counts = df['has_hypertension'].value_counts()
            labels = ['No Hypertension', 'Hypertension']
            colors = ['lightblue', 'orange']

            plt.pie(hypertension_counts.values, labels=labels, colors=colors, autopct='%1.1f%%',
                   startangle=90, textprops={'fontsize': self.font_size - 2})
            plt.title('Hypertension Distribution - Complete Population', fontsize=self.font_size + 2)
            plt.tight_layout()
            plt.savefig('plots/hypertension_distribution_simple.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("Hypertension distribution plot saved to: plots/hypertension_distribution_simple.png")

    def create_top_medications_plot(self, df, med_data, top_n=10):
        """Create plot for top medications in complete population."""
        print(f"\n{'-'*50}")
        print("CREATING TOP MEDICATIONS PLOT")
        print(f"{'-'*50}")

        os.makedirs('plots', exist_ok=True)

        if len(med_data) > 0:
            # Get top medications by ATC designation for complete population, excluding nulls
            med_data_clean = med_data.dropna(subset=['ATCBezeichnung']).query("ATCBezeichnung != '(null)'")
            top_meds = med_data_clean['ATCBezeichnung'].value_counts().head(top_n)

            plt.figure(figsize=(14, 8))
            bars = plt.barh(range(len(top_meds)), top_meds.values, color='steelblue')
            plt.yticks(range(len(top_meds)), [med[:50] + '...' if len(med) > 50 else med for med in top_meds.index])
            plt.xlabel('Number of Patients', fontsize=self.font_size)
            plt.ylabel('Medication (ATC Designation)', fontsize=self.font_size)
            plt.title(f'Top {top_n} Medications in Complete Population', fontsize=self.font_size + 2)
            plt.gca().invert_yaxis()

            # Add value labels on bars
            for i, bar in enumerate(bars):
                width = bar.get_width()
                plt.text(width + 0.5, bar.get_y() + bar.get_height()/2,
                        f'{int(width)}', ha='left', va='center', fontsize=self.font_size - 2)

            plt.tight_layout()
            plt.savefig('plots/top_medications_complete.png', dpi=300, bbox_inches='tight')
            plt.close()
            print(f"Top {top_n} medications plot saved to: plots/top_medications_complete.png")
        else:
            print("No medication data available")

    def create_top_diagnoses_plot(self, df, conn, top_n=10):
        """Create plot for top diagnoses in complete population."""
        print(f"\n{'-'*50}")
        print("CREATING TOP DIAGNOSES PLOT")
        print(f"{'-'*50}")

        os.makedirs('plots', exist_ok=True)

        # Query for all diagnoses in complete population
        query = """
        SELECT
            d.PrimaerdiagnoseICD,
            d.PrimaerdiagnoseText,
            d.SekundaerdiagnoseICD,
            d.SekundaerdiagnoseText
        FROM Diagnosen d
        """

        try:
            diagnoses_df = pd.read_sql_query(query, conn)

            # Combine primary and secondary diagnoses
            all_diagnoses = []

            # Add primary diagnoses
            primary_diag = diagnoses_df[['PrimaerdiagnoseICD', 'PrimaerdiagnoseText']].dropna().query("PrimaerdiagnoseText != '(null)'")
            for _, row in primary_diag.iterrows():
                icd_code = row['PrimaerdiagnoseICD']
                diag_text = row['PrimaerdiagnoseText']
                # Exclude OSA, Z01.7, and Sleep Apnea
                if (icd_code and
                    not icd_code.startswith('G47.31') and
                    not icd_code.startswith('Z01.7') and
                    'sleep apnea' not in diag_text.lower() and
                    'schlafapnoe' not in diag_text.lower()):
                    all_diagnoses.append(f"{icd_code}: {diag_text}")

            # Add secondary diagnoses
            secondary_diag = diagnoses_df[['SekundaerdiagnoseICD', 'SekundaerdiagnoseText']].dropna().query("SekundaerdiagnoseText != '(null)'")
            for _, row in secondary_diag.iterrows():
                icd_code = row['SekundaerdiagnoseICD']
                diag_text = row['SekundaerdiagnoseText']
                # Exclude OSA, Z01.7, and Sleep Apnea
                if (icd_code and
                    not icd_code.startswith('G47.31') and
                    not icd_code.startswith('Z01.7') and
                    'sleep apnea' not in diag_text.lower() and
                    'schlafapnoe' not in diag_text.lower()):
                    all_diagnoses.append(f"{icd_code}: {diag_text}")

            if all_diagnoses:
                # Count diagnoses
                diagnosis_counts = pd.Series(all_diagnoses).value_counts().head(top_n)

                plt.figure(figsize=(14, 8))
                bars = plt.barh(range(len(diagnosis_counts)), list(diagnosis_counts.values), color='lightcoral')
                plt.yticks(range(len(diagnosis_counts)),
                          [diag[:60] + '...' if len(diag) > 60 else diag for diag in diagnosis_counts.index])
                plt.xlabel('Number of Patients', fontsize=self.font_size)
                plt.ylabel('Diagnosis (ICD Code: Description)', fontsize=self.font_size)
                plt.title(f'Top {top_n} Diagnoses in Complete Population', fontsize=self.font_size + 2)
                plt.gca().invert_yaxis()

                # Add value labels on bars
                for i, bar in enumerate(bars):
                    width = bar.get_width()
                    plt.text(width + 0.5, bar.get_y() + bar.get_height()/2,
                            f'{int(width)}', ha='left', va='center', fontsize=self.font_size - 2)

                plt.tight_layout()
                plt.savefig('plots/top_diagnoses_complete.png', dpi=300, bbox_inches='tight')
                plt.close()
                print(f"Top {top_n} diagnoses plot saved to: plots/top_diagnoses_complete.png")
            else:
                print("No diagnosis data available")

        except Exception as e:
            print(f"Error creating diagnoses plot: {e}")

    def create_quality_check_plots(self, df, conn):
        """Create quality check plots for BMI vs Adipositas and Hypertension medication vs Diagnosed Hypertension."""
        print(f"\n{'-'*50}")
        print("CREATING QUALITY CHECK PLOTS")
        print(f"{'-'*50}")

        os.makedirs('plots', exist_ok=True)

        # Quality check 1: BMI > 30 vs Diagnosed Adipositas
        self._create_bmi_adipositas_check(df, conn)

        # Quality check 2: Hypertension medication vs Diagnosed Hypertension
        self._create_hypertension_medication_check(df)

    def _create_bmi_adipositas_check(self, df, conn):
        """Create quality check plot for BMI > 30 vs Diagnosed Adipositas."""
        try:
            # Get patients with adipositas diagnosis (E66)
            adipositas_query = """
            SELECT DISTINCT d.PID
            FROM Diagnosen d
            WHERE (d.PrimaerdiagnoseICD LIKE 'E66%' OR d.SekundaerdiagnoseICD LIKE 'E66%')
            """
            adipositas_patients = pd.read_sql_query(adipositas_query, conn)

            # Add adipositas flag to dataframe
            df_check = df.copy()
            df_check['has_adipositas_diagnosis'] = df_check['PID'].isin(adipositas_patients['PID']).astype(int)

            # Create BMI > 30 flag
            if 'BMI' in df_check.columns:
                df_check['bmi_over_30'] = (df_check['BMI'] > 30).astype(int)

                # Create contingency table
                contingency = pd.crosstab(df_check['bmi_over_30'], df_check['has_adipositas_diagnosis'],
                                        margins=True, margins_name='Total')

                # Create visualization
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

                # Heatmap of contingency table
                sns.heatmap(contingency.iloc[:-1, :-1], annot=True, fmt='d', cmap='Blues', ax=ax1)
                ax1.set_title('BMI > 30 vs Adipositas Diagnosis', fontsize=self.font_size + 2)
                ax1.set_xlabel('Has Adipositas Diagnosis', fontsize=self.font_size)
                ax1.set_ylabel('BMI > 30', fontsize=self.font_size)
                ax1.set_xticklabels(['No', 'Yes'])
                ax1.set_yticklabels(['No', 'Yes'])

                # Calculate agreement metrics
                both_positive = len(df_check[(df_check['bmi_over_30'] == 1) & (df_check['has_adipositas_diagnosis'] == 1)])
                both_negative = len(df_check[(df_check['bmi_over_30'] == 0) & (df_check['has_adipositas_diagnosis'] == 0)])
                total_with_bmi = len(df_check.dropna(subset=['BMI']))
                agreement = (both_positive + both_negative) / total_with_bmi * 100 if total_with_bmi > 0 else 0

                # Bar plot showing agreement
                categories = ['BMI>30 & Adipositas Dx', 'BMI≤30 & No Adipositas Dx', 'Disagreement']
                values = [both_positive, both_negative, total_with_bmi - both_positive - both_negative]
                colors = ['green', 'green', 'red']

                bars = ax2.bar(categories, values, color=colors, alpha=0.7)
                ax2.set_title(f'Quality Check: BMI vs Adipositas Diagnosis\n(Agreement: {agreement:.1f}%)',
                             fontsize=self.font_size + 2)
                ax2.set_ylabel('Number of Patients', fontsize=self.font_size)
                ax2.tick_params(axis='x', rotation=45)

                # Add value labels on bars
                for bar in bars:
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                            f'{int(height)}', ha='center', va='bottom', fontsize=self.font_size - 2)

                plt.tight_layout()
                plt.savefig('plots/quality_check_bmi_adipositas.png', dpi=300, bbox_inches='tight')
                plt.close()
                print("BMI vs Adipositas quality check plot saved to: plots/quality_check_bmi_adipositas.png")

                # Print summary
                print(f"BMI vs Adipositas Quality Check Summary:")
                print(f"  Total patients with BMI data: {total_with_bmi}")
                print(f"  BMI > 30 with Adipositas diagnosis: {both_positive}")
                print(f"  BMI ≤ 30 without Adipositas diagnosis: {both_negative}")
                print(f"  Overall agreement: {agreement:.1f}%")
            else:
                print("BMI data not available for quality check")

        except Exception as e:
            print(f"Error creating BMI vs Adipositas quality check: {e}")

    def _create_hypertension_medication_check(self, df):
        """Create quality check plot for Hypertension medication vs Diagnosed Hypertension."""
        if 'has_diagnosed_hypertension' in df.columns and 'on_antihypertensives' in df.columns:
            # Create contingency table
            contingency = pd.crosstab(df['on_antihypertensives'], df['has_diagnosed_hypertension'],
                                    margins=True, margins_name='Total')

            # Create visualization
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

            # Heatmap of contingency table
            sns.heatmap(contingency.iloc[:-1, :-1], annot=True, fmt='d', cmap='Oranges', ax=ax1)
            ax1.set_title('Antihypertensive Medication vs Hypertension Diagnosis', fontsize=self.font_size + 2)
            ax1.set_xlabel('Has Hypertension Diagnosis', fontsize=self.font_size)
            ax1.set_ylabel('On Antihypertensive Medication', fontsize=self.font_size)
            ax1.set_xticklabels(['No', 'Yes'])
            ax1.set_yticklabels(['No', 'Yes'])

            # Calculate agreement metrics
            both_positive = len(df[(df['on_antihypertensives'] == 1) & (df['has_diagnosed_hypertension'] == 1)])
            both_negative = len(df[(df['on_antihypertensives'] == 0) & (df['has_diagnosed_hypertension'] == 0)])
            total_patients = len(df)
            agreement = (both_positive + both_negative) / total_patients * 100

            # Bar plot showing agreement
            categories = ['Medication & Diagnosis', 'No Medication & No Diagnosis', 'Disagreement']
            values = [both_positive, both_negative, total_patients - both_positive - both_negative]
            colors = ['green', 'green', 'red']

            bars = ax2.bar(categories, values, color=colors, alpha=0.7)
            ax2.set_title(f'Quality Check: Medication vs Diagnosis\n(Agreement: {agreement:.1f}%)',
                         fontsize=self.font_size + 2)
            ax2.set_ylabel('Number of Patients', fontsize=self.font_size)
            ax2.tick_params(axis='x', rotation=45)

            # Add value labels on bars
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{int(height)}', ha='center', va='bottom', fontsize=self.font_size - 2)

            plt.tight_layout()
            plt.savefig('plots/quality_check_hypertension_medication.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("Hypertension medication vs diagnosis quality check plot saved to: plots/quality_check_hypertension_medication.png")

            # Print summary
            print(f"Hypertension Medication vs Diagnosis Quality Check Summary:")
            print(f"  Total patients: {total_patients}")
            print(f"  On medication with diagnosis: {both_positive}")
            print(f"  No medication without diagnosis: {both_negative}")
            print(f"  Overall agreement: {agreement:.1f}%")
        else:
            print("Hypertension data not available for quality check")

def main():
    """Create enhanced gender-stratified ROC curves and additional visualizations."""

    parser = argparse.ArgumentParser(description='Create enhanced gender-stratified ROC curves with additional visualizations')
    parser.add_argument('--model', choices=['stop_bang', 'random_forest'],
                       default='random_forest',
                       help='Model to analyze (default: random_forest)')
    parser.add_argument('--font-size', type=int, default=16,
                       help='Font size for plots (default: 16)')

    args = parser.parse_args()

    # Load the data
    print("Loading data...")
    df, atc_classes = load_and_merge_data()

    # Initialize enhanced visualization generator
    viz_generator = EnhancedVisualizationGenerator(font_size=args.font_size)

    # Connect to database for additional queries
    db_connector = DatabaseConnector('../patient_management.db')
    conn = db_connector.get_connection()
    extractor = DataExtractor(conn)

    # Initialize analysis_df
    analysis_df = df

    # Create enhanced ROC curves and calculate subgroup metrics
    if args.model == 'stop_bang':
        print("Calculating STOP-BANG scores...")
        stop_bang_analyzer = StopBangAnalyzer()
        stop_bang_df = stop_bang_analyzer.analyze(df)

        print("Creating enhanced gender-stratified ROC curves for STOP-BANG model...")
        gender_metrics = viz_generator.create_enhanced_roc_curve(
            df=stop_bang_df,
            model_column='partial_stop_bang_score',
            model_name='STOP-BANG',
            save_path='plots/stop_bang_gender_roc_enhanced.png'
        )
        analysis_df = stop_bang_df

        # Calculate subgroup metrics
        print("\nCalculating subgroup sensitivity and specificity...")
        viz_generator.calculate_subgroup_metrics(
            df=stop_bang_df,
            model_column='partial_stop_bang_score',
            model_name='STOP-BANG'
        )

    elif args.model == 'random_forest':
        print("Creating enhanced gender-stratified ROC curves for Random Forest model...")
        gender_metrics = viz_generator.create_enhanced_roc_curve(
            df=df,
            model_column='dummy_rf_column',  # This will be handled in _calculate_rf_roc
            model_name='Random Forest',
            save_path='plots/rf_gender_roc_enhanced.png',
            atc_classes=atc_classes
        )
        analysis_df = df

        # Calculate subgroup metrics
        print("\nCalculating subgroup sensitivity and specificity...")
        viz_generator.calculate_subgroup_metrics(
            df=df,
            model_column='dummy_rf_column',
            model_name='Random Forest',
            atc_classes=atc_classes
        )

    # Create additional visualizations
    print("\nCreating distribution plots...")
    viz_generator.create_distribution_plots(analysis_df)

    print("\nCreating top medications plot...")
    med_data = extractor.get_medication_data()
    viz_generator.create_top_medications_plot(analysis_df, med_data, top_n=10)

    print("\nCreating top diagnoses plot...")
    viz_generator.create_top_diagnoses_plot(analysis_df, conn, top_n=10)

    print("\nCreating quality check plots...")
    viz_generator.create_quality_check_plots(analysis_df, conn)

    # Close database connection
    db_connector.close_connection()

    print(f"\n{'='*60}")
    print(f"ENHANCED ANALYSIS COMPLETE FOR {args.model.upper()} MODEL!")
    print(f"{'='*60}")
    print("Generated plots:")
    print(f"  - Enhanced ROC curve: plots/{args.model}_gender_roc_enhanced.png")
    print("  - Age distribution: plots/age_distribution_simple.png")
    print("  - BMI distribution: plots/bmi_distribution_simple.png")
    print("  - Hypertension distribution: plots/hypertension_distribution_simple.png")
    print("  - Top medications: plots/top_medications_complete.png")
    print("  - Top diagnoses: plots/top_diagnoses_complete.png")
    print("  - Quality check BMI vs Adipositas: plots/quality_check_bmi_adipositas.png")
    print("  - Quality check Hypertension: plots/quality_check_hypertension_medication.png")
    print(f"\nAll plots use font size: {args.font_size}")
    print("Check the 'plots' directory for all generated visualizations.")

if __name__ == "__main__":
    main()
