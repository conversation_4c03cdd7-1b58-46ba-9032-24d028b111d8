"""
Data Extraction Module
--------------------
Extracts data from the database.
"""

import pandas as pd

class DataExtractor:
    """Extracts data from the database."""
    
    def __init__(self, conn):
        """
        Initialize the data extractor.
        
        Args:
            conn: SQLite database connection
        """
        self.conn = conn
    
    def get_suspected_osa_patients(self):
        """
        Get patients with suspected Obstructive Sleep Apnea (G47.31).
        
        Returns:
            DataFrame: Patient IDs with OSA
        """
        query = """
        SELECT DISTINCT d1.PID 
        FROM Diagnosen d1
        WHERE 
        d1.DiagnoseTyp IN ('Aufnahmediagnose') 
        and (d1.PrimaerdiagnoseICD LIKE 'G47.31%' OR d1.SekundaerdiagnoseICD LIKE 'G47.31%')
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_extern_suspected_osa_patients(self):
        """
        Get patients with suspected Obstructive Sleep Apnea (G47.31).
        
        Returns:
            DataFrame: Patient IDs with OSA
        """
        query = """
        SELECT DISTINCT d.PID 
        FROM Diagnosen d 
        WHERE d.Diagnosesicherheit = 'Verdacht auf' and
        (d.PrimaerdiagnoseICD LIKE 'G47.31%' OR d.SekundaerdiagnoseICD LIKE 'G47.31%')
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_unsuspected_osa_patients(self):
        """
        Get patients with Obstructive Sleep Apnea (G47.31).
        
        Returns:
            DataFrame: Patient IDs with OSA
        """
        query = """
        SELECT DISTINCT d1.PID 
        FROM Diagnosen d1
        LEFT JOIN Diagnosen d2 ON d1.PID = d2.PID
        WHERE 
        d1.DiagnoseTyp IN ('Aufnahmediagnose') 
        and        (d1.PrimaerdiagnoseICD not LIKE 'G47.31%' AND d1.SekundaerdiagnoseICD not LIKE 'G47.31%')
        and        (d2.PrimaerdiagnoseICD LIKE 'G47.31%' OR d2.SekundaerdiagnoseICD LIKE 'G47.31%')
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_osa_patients(self):
        """
        Get patients with Obstructive Sleep Apnea (G47.31).
        
        Returns:
            DataFrame: Patient IDs with OSA
        """
        query = """
        SELECT DISTINCT d.PID 
        FROM Diagnosen d 
        
        WHERE
        d.DiagnoseTyp IN ('Entlassdiagnose') AND
        (d.PrimaerdiagnoseICD LIKE 'G47.31%' OR d.SekundaerdiagnoseICD LIKE 'G47.31%')
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_sleep_study_patients(self):
        """
        Get patients who underwent sleep study (OPS 1-790) with date.
        
        Returns:
            DataFrame: Patient IDs with sleep study dates
        """
        query = """
        SELECT p.PID, MAX(p.Durchfuehrungsdatum) as sleep_study_date
        FROM Prozeduren p 
        WHERE p.OPSCode LIKE '1-790%'
        GROUP BY p.PID
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_patient_demographics(self):
        """
        Get patient demographics including age and sex.
        
        Returns:
            DataFrame: Patient demographics
        """
        query = """
        SELECT 
            p.PID,
            p.AdministrativesGeschlecht as Sex,
            p.Geburtsdatum
        FROM Patient p
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_hypertension_patients(self):
        """
        Get patients with diagnosed hypertension (ICD I10-I15).
        
        Returns:
            DataFrame: Patient IDs with hypertension
        """
        query = """
        SELECT DISTINCT d.PID 
        FROM Diagnosen d 
        WHERE (d.PrimaerdiagnoseICD LIKE 'I1%' OR d.SekundaerdiagnoseICD LIKE 'I1%')
        AND (
            SUBSTR(d.PrimaerdiagnoseICD, 1, 3) BETWEEN 'I10' AND 'I15'
            OR SUBSTR(d.SekundaerdiagnoseICD, 1, 3) BETWEEN 'I10' AND 'I15'
        )
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_patients_on_antihypertensives(self):
        """
        Get patients on antihypertensive medications based on ATC codes.
        
        Returns:
            DataFrame: Patient IDs on antihypertensives
        """
        # ATC codes for antihypertensive medications
        atc_codes = [
            'C02%',  # Antihypertensiva
            'C03%',  # Diuretika
            'C07%',  # Beta-Adrenorezeptorantagonisten
            'C08%',  # Calciumkanalblocker
            'C09%'   # Mittel mit Wirkung auf das Renin-Angiotensin-System
        ]
        
        # Build the query with OR conditions for each ATC code
        atc_conditions = " OR ".join([f"m.ATCCode LIKE '{code}'" for code in atc_codes])
        
        query = f"""
        SELECT DISTINCT m.PID 
        FROM Medikation m 
        WHERE {atc_conditions}
        """
        
        return pd.read_sql_query(query, self.conn)
    
    def get_diabetes_patients(self):
        """
        Get patients with diabetes (ICD E10-E14).
        
        Returns:
            DataFrame: Patient IDs with diabetes
        """
        query = """
        SELECT DISTINCT d.PID 
        FROM Diagnosen d 
        WHERE (d.PrimaerdiagnoseICD LIKE 'E1%' OR d.SekundaerdiagnoseICD LIKE 'E1%')
        AND (
            SUBSTR(d.PrimaerdiagnoseICD, 1, 3) BETWEEN 'E10' AND 'E14'
            OR SUBSTR(d.SekundaerdiagnoseICD, 1, 3) BETWEEN 'E10' AND 'E14'
        )
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_cardiovascular_patients(self):
        """
        Get patients with cardiovascular diseases (ICD I20-I25).
        
        Returns:
            DataFrame: Patient IDs with cardiovascular disease
        """
        query = """
        SELECT DISTINCT d.PID 
        FROM Diagnosen d 
        WHERE (
            (d.PrimaerdiagnoseICD LIKE 'I2%' OR d.SekundaerdiagnoseICD LIKE 'I2%')
            AND (
                SUBSTR(d.PrimaerdiagnoseICD, 1, 3) BETWEEN 'I20' AND 'I25'
                OR SUBSTR(d.SekundaerdiagnoseICD, 1, 3) BETWEEN 'I20' AND 'I25'
            )
        )
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_medication_data(self):
        """
        Get medication data for patients.
        
        Returns:
            DataFrame: Medication data
        """
        query = """
        SELECT 
            m.PID,
            m.Medikationsbezeichnung,
            m.ATCCode,
            m.ATCBezeichnung
        FROM Medikation m
        """
        return pd.read_sql_query(query, self.conn)
    
    def get_medication_by_atc_class(self):
        """
        Get patients on specific medication classes by ATC code.
        
        Returns:
            tuple: (dict of DataFrames by medication class, dict of ATC classes)
        """
        atc_classes = {
            'C02': 'Antihypertensiva',
            'C03': 'Diuretika',
            'C07': 'Beta-Blocker',
            'C08': 'Calciumkanalblocker',
            'C09': 'ACE-Hemmer/ARBs'
        }
        
        result = {}
        
        for atc_prefix, name in atc_classes.items():
            query = f"""
            SELECT DISTINCT m.PID 
            FROM Medikation m 
            WHERE m.ATCCode LIKE '{atc_prefix}%'
            """
            df = pd.read_sql_query(query, self.conn)
            df[f'on_{name.lower().replace("-", "_")}'] = 1
            result[name] = df
        
        return result, atc_classes
    
    def get_bmi_data(self):
        """
        Get BMI data from vital signs or calculate it from height and weight.
        
        Returns:
            DataFrame: BMI data
        """
        # First try to get direct BMI measurements
        bmi_query = """
        SELECT 
            v.PID,
            v.Vitalwert as BMI
        FROM Vitaldaten v
        WHERE v.Vitalparameter LIKE '%BMI%' OR v.Text LIKE '%BMI%'
        """
        bmi_df = pd.read_sql_query(bmi_query, self.conn)
        
        # Convert BMI to numeric, handling potential errors
        if not bmi_df.empty:
            bmi_df['BMI'] = pd.to_numeric(bmi_df['BMI'], errors='coerce')
        
        # Get height data
        height_query = """
        SELECT 
            v.PID,
            v.Vitalwert as Height,
            v.Masseinheit as Height_Unit
        FROM Vitaldaten v
        WHERE v.Vitalparameter LIKE '%Größe%' OR v.Text LIKE '%Größe%' OR v.Vitalparameter LIKE '%Height%'
        """
        height_df = pd.read_sql_query(height_query, self.conn)
        
        # Get weight data
        weight_query = """
        SELECT 
            v.PID,
            v.Vitalwert as Weight,
            v.Masseinheit as Weight_Unit
        FROM Vitaldaten v
        WHERE v.Vitalparameter LIKE '%Gewicht%' OR v.Text LIKE '%Gewicht%' OR v.Vitalparameter LIKE '%Weight%'
        """
        weight_df = pd.read_sql_query(weight_query, self.conn)
        
        # Convert height and weight to numeric
        if not height_df.empty:
            height_df['Height'] = pd.to_numeric(height_df['Height'], errors='coerce')
        
        if not weight_df.empty:
            weight_df['Weight'] = pd.to_numeric(weight_df['Weight'], errors='coerce')
        
        # Merge height and weight data
        hw_df = pd.merge(height_df, weight_df, on='PID', how='inner')
        
        # Calculate BMI for patients with height and weight
        if not hw_df.empty:
            # Convert height to meters if in cm
            hw_df['Height_m'] = hw_df.apply(
                lambda row: row['Height'] / 100 if (pd.notna(row['Height_Unit']) and row['Height_Unit'] == 'cm') or 
                                                   (pd.isna(row['Height_Unit']) and row['Height'] > 3) 
                            else row['Height'], 
                axis=1
            )
            
            # Convert weight to kg if in g
            hw_df['Weight_kg'] = hw_df.apply(
                lambda row: row['Weight'] / 1000 if (pd.notna(row['Weight_Unit']) and row['Weight_Unit'] == 'g')
                            else row['Weight'], 
                axis=1
            )
            
            # Calculate BMI: weight(kg) / height(m)²
            hw_df['Calculated_BMI'] = hw_df['Weight_kg'] / (hw_df['Height_m'] ** 2)
            
            # Create a dataframe with calculated BMIs
            calc_bmi_df = hw_df[['PID', 'Calculated_BMI']].rename(columns={'Calculated_BMI': 'BMI'})
            
            # Combine direct BMI measurements with calculated BMIs
            if not bmi_df.empty:
                # For patients with both measured and calculated BMI, prefer the measured one
                combined_df = pd.concat([bmi_df, calc_bmi_df])
                combined_df = combined_df.drop_duplicates(subset=['PID'], keep='first')
            else:
                combined_df = calc_bmi_df
        else:
            combined_df = bmi_df
        
        # Group by patient and take the average BMI (in case of multiple measurements)
        if not combined_df.empty:
            result_df = combined_df.groupby('PID')['BMI'].mean().reset_index()
            
            # Filter out implausible BMI values
            result_df = result_df[(result_df['BMI'] > 10) & (result_df['BMI'] < 100)]
            
            return result_df
        else:
            return pd.DataFrame(columns=['PID', 'BMI'])
    
    def extract_all_data(self):
        """Extract and merge all required data for analysis.
        
        Returns:
            tuple: (merged_dataframe, top_osa_meds, top_osa_atc, atc_classes)
        """
        # Get all patient data
        all_patients = pd.read_sql_query("SELECT DISTINCT PID FROM Patient", self.conn)
        
        # Get OSA patients
        osa_patients = self.get_osa_patients()
        osa_patients['has_osa'] = 1

        suspected_osa_patients = self.get_suspected_osa_patients()
        suspected_osa_patients['has_suspected_osa'] = 1

        unsuspected_osa_patients = self.get_unsuspected_osa_patients()
        unsuspected_osa_patients['has_unsuspected_osa'] = 1
        
        # Get sleep study patients
        sleep_study_patients = self.get_sleep_study_patients()
        sleep_study_patients['had_sleep_study'] = 1
        
        # Get demographics
        demographics = self.get_patient_demographics()
        
        # Get comorbidities
        hypertension_patients = self.get_hypertension_patients()
        hypertension_patients['has_diagnosed_hypertension'] = 1
        
        # Get patients on antihypertensive medications
        antihypertensive_patients = self.get_patients_on_antihypertensives()
        antihypertensive_patients['on_antihypertensives'] = 1
        
        # Get specific antihypertensive medication classes
        med_classes, atc_classes = self.get_medication_by_atc_class()
        
        diabetes_patients = self.get_diabetes_patients()
        diabetes_patients['has_diabetes'] = 1
                
        cardiovascular_patients = self.get_cardiovascular_patients()
        cardiovascular_patients['has_cardiovascular'] = 1
        
        # Get BMI data
        bmi_data = self.get_bmi_data()
        
        # Merge all data
        df = sleep_study_patients
        df = df.merge(osa_patients[['PID', 'has_osa']], on='PID', how='left')
        df['has_osa'] = df['has_osa'].fillna(0)

        # Convert dates to datetime
        df['sleep_study_date'] = pd.to_datetime(df['sleep_study_date'], errors='coerce')
        demographics['Geburtsdatum'] = pd.to_datetime(demographics['Geburtsdatum'], errors='coerce')

        df = df.merge(demographics[['PID', 'Sex', 'Geburtsdatum']], on='PID', how='left')

        df['Age'] = (df['sleep_study_date'] - df['Geburtsdatum']).dt.days / 365.25
        df = df.merge(hypertension_patients[['PID', 'has_diagnosed_hypertension']], on='PID', how='left')
        df = df.merge(antihypertensive_patients[['PID', 'on_antihypertensives']], on='PID', how='left')
        
        # Merge specific medication classes
        for name, med_df in med_classes.items():
            col_name = f'on_{name.lower().replace("-", "_")}'
            if col_name in med_df.columns:
                df = df.merge(med_df[['PID', col_name]], on='PID', how='left')
        
        df = df.merge(diabetes_patients[['PID', 'has_diabetes']], on='PID', how='left')
        df = df.merge(cardiovascular_patients[['PID', 'has_cardiovascular']], on='PID', how='left')
        df = df.merge(bmi_data, on='PID', how='left')

        df = df.merge(suspected_osa_patients[['PID', 'has_suspected_osa']], on='PID', how='left')
        df = df.merge(unsuspected_osa_patients[['PID', 'has_unsuspected_osa']], on='PID', how='left')
        
        # Fill NAs for binary variables
        binary_cols = [col for col in df.columns if col.startswith(('has_', 'had_', 'on_'))]
        df[binary_cols] = df[binary_cols].fillna(0)
        
        # Create combined hypertension variable (diagnosed OR on medications)
        df['has_hypertension'] = ((df['has_diagnosed_hypertension'] == 1) | 
                                 (df['on_antihypertensives'] == 1)).astype(int)
        
        # Analyze medication patterns
        med_data = self.get_medication_data()
        
        # Get the most common medications for OSA patients
        osa_meds = med_data[med_data['PID'].isin(osa_patients['PID'])]
        top_osa_meds = osa_meds['ATCBezeichnung'].value_counts().head(10)
        
        # Get the most common ATC codes for OSA patients
        top_osa_atc = osa_meds['ATCCode'].apply(lambda x: x[:3] if pd.notna(x) else x).value_counts().head(10)
        
        return df, top_osa_meds, top_osa_atc, atc_classes
