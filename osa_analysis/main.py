#!/usr/bin/env python3
"""
OSA Analysis Main Module
-----------------------
Main entry point for the OSA analysis application.
"""

import os
import sys
from osa_analysis.data.database import DatabaseConnector
from osa_analysis.data.queries import DataExtractor
from osa_analysis.analysis.processor import DataProcessor
from osa_analysis.analysis.statistics import StatisticsGenerator
from osa_analysis.analysis.stop_bang import StopBangAnalyzer
from osa_analysis.analysis.modeling import PredictiveModeler
from osa_analysis.analysis.model_evaluation import ModelEvaluator
from osa_analysis.visualization.plots import Visualizer

def main():
    """Main entry point for the OSA analysis application."""
    print("Starting OSA Analysis...")
    
    # Connect to the database
    db_connector = DatabaseConnector('../patient_management.db')
    conn = db_connector.get_connection()
    
    # Extract data
    print("Extracting data from database...")
    extractor = DataExtractor(conn)
    
    # Get patient data
    osa_patients = extractor.get_osa_patients()
    suspected_osa_patients = extractor.get_suspected_osa_patients()
    unsuspected_osa_patients = extractor.get_unsuspected_osa_patients()
    sleep_study_patients = extractor.get_sleep_study_patients()
    demographics = extractor.get_patient_demographics()
    hypertension_patients = extractor.get_hypertension_patients()
    antihypertensive_patients = extractor.get_patients_on_antihypertensives()
    diabetes_patients = extractor.get_diabetes_patients()
    cardiovascular_patients = extractor.get_cardiovascular_patients()
    bmi_data = extractor.get_bmi_data()
    
    # Get medication data
    med_data = extractor.get_medication_data()
    med_classes, atc_classes = extractor.get_medication_by_atc_class()
    
    # Process data
    print("Processing data...")
    processor = DataProcessor()
    df = processor.merge_data(
        sleep_study_patients, 
        osa_patients,
        suspected_osa_patients,
        unsuspected_osa_patients,
        demographics,
        hypertension_patients,
        antihypertensive_patients,
        med_classes,
        diabetes_patients,
        cardiovascular_patients,
        bmi_data
    )
    
    # Get medication patterns
    top_osa_meds = processor.get_top_medications(med_data, osa_patients)
    top_osa_atc = processor.get_top_atc_codes(med_data, osa_patients)
    
    # Generate statistics
    print("Generating statistics...")
    stats_generator = StatisticsGenerator()
    stats_generator.generate_statistics(df, top_osa_meds, top_osa_atc, atc_classes)
    
    # Perform STOP-BANG analysis
    print("Performing STOP-BANG analysis...")
    stop_bang_analyzer = StopBangAnalyzer()
    stop_bang_df = stop_bang_analyzer.analyze(df)
    
    # Build predictive models
    print("Building predictive models...")
    modeler = PredictiveModeler()
    modeler.build_models(df, atc_classes)
    
    # Evaluate model performance across demographic groups
    print("Evaluating model performance across demographic groups...")
    evaluator = ModelEvaluator()
    evaluator.evaluate_across_groups(df, atc_classes)
    
    # Create visualizations
    print("Creating visualizations...")
    visualizer = Visualizer()
    visualizer.create_visualizations(df, atc_classes)
    visualizer.create_stop_bang_visualizations(stop_bang_df)
    
    # Close database connection
    db_connector.close_connection()
    
    print("Analysis complete!")

if __name__ == "__main__":
    main()
